/**
 * Extension Bridge Script
 * This script handles communication between the browser extension and the video editor
 * It should be included in the video editor's HTML to receive data from the extension
 */

(function() {
    'use strict';
    
    console.log('Extension bridge script loaded');

    // Listen for messages from the browser extension
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('Extension bridge received message:', message);
        
        if (message.type === 'RECORDING_COMPLETE' && message.source === 'extension') {
            handleRecordingData(message);
            sendResponse({ received: true });
        }
        
        return true; // Keep message channel open
    });

    // Also listen for window messages (fallback method)
    window.addEventListener('message', (event) => {
        // Only accept messages from same origin
        if (event.origin !== window.location.origin) return;
        
        const message = event.data;
        if (message.type === 'RECORDING_COMPLETE' && message.source === 'extension') {
            console.log('Extension bridge received window message:', message);
            handleRecordingData(message);
        }
    });

    function handleRecordingData(message) {
        try {
            console.log('Processing recording data from extension...');
            
            // Convert base64 back to blob if needed
            let videoBlob = message.data.videoBlob;
            if (typeof videoBlob === 'string' && videoBlob.startsWith('data:')) {
                videoBlob = base64ToBlob(videoBlob);
            }

            // Prepare the data for the video editor
            const recordingData = {
                videoBlob: videoBlob,
                mouseData: message.data.mouseData || [],
                recordingInfo: message.data.recordingInfo || {
                    totalCoordinates: 0,
                    totalClicks: 0,
                    timestamp: new Date().toISOString()
                }
            };

            // Send to the video editor's communication service
            window.postMessage({
                type: 'RECORDING_COMPLETE',
                source: 'extension',
                timestamp: Date.now(),
                data: recordingData
            }, window.location.origin);

            console.log('Recording data forwarded to video editor');
            
        } catch (error) {
            console.error('Error handling recording data:', error);
        }
    }

    function base64ToBlob(base64Data) {
        try {
            const byteCharacters = atob(base64Data.split(',')[1]);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: 'video/webm' });
        } catch (error) {
            console.error('Error converting base64 to blob:', error);
            return null;
        }
    }

    // Notify extension that the bridge is ready
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        try {
            chrome.runtime.sendMessage({
                type: 'BRIDGE_READY',
                source: 'editor',
                timestamp: Date.now()
            });
        } catch (error) {
            // Extension might not be available, which is fine
            console.log('Extension not available for bridge ready notification');
        }
    }

})();
