import { useEffect, useCallback, useState } from 'react';
import { extensionCommunication, RecordingData, MouseTrackingData } from '../../../services/extension-communication';
import { useLocalVideosStore } from '../store/use-local-videos-store';
import { dispatch } from '@designcombo/events';
import { ADD_VIDEO } from '@designcombo/state';
import { generateId } from '@designcombo/timeline';
import { IVideo } from '@designcombo/types';

export interface ExternalDataState {
  isLoading: boolean;
  hasData: boolean;
  error: string | null;
  videoFile: File | null;
  mouseData: MouseTrackingData[];
  metadata: RecordingData['recordingInfo'] | null;
}

export interface UseExternalDataLoaderReturn {
  state: ExternalDataState;
  loadExternalData: (data: RecordingData) => Promise<void>;
  clearExternalData: () => void;
  checkExtensionConnection: () => Promise<boolean>;
}

/**
 * Hook for handling external data loading from browser extension
 */
export function useExternalDataLoader(): UseExternalDataLoaderReturn {
  const { actions: videoActions } = useLocalVideosStore();
  
  const [state, setState] = useState<ExternalDataState>({
    isLoading: false,
    hasData: false,
    error: null,
    videoFile: null,
    mouseData: [],
    metadata: null
  });

  /**
   * Handle recording complete message from extension
   */
  const handleRecordingComplete = useCallback(async (message: any) => {
    if (!message.data || !extensionCommunication.isValidRecordingData(message.data)) {
      setState(prev => ({
        ...prev,
        error: 'Invalid recording data received from extension'
      }));
      return;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      await loadExternalData(message.data);
    } catch (error) {
      console.error('Error loading external recording data:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load recording data'
      }));
    }
  }, []);

  /**
   * Load external recording data
   */
  const loadExternalData = useCallback(async (data: RecordingData) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Process the recording data
      const { videoFile, mouseData, metadata } = extensionCommunication.processRecordingData(data);

      // Add video to local videos store
      const localVideo = await videoActions.addVideo(videoFile);

      // Create video data for timeline
      const videoData: Partial<IVideo> = {
        id: generateId(),
        details: {
          src: localVideo.objectUrl,
          width: localVideo.width,
          height: localVideo.height,
          blur: 0,
          brightness: 100,
          flipX: false,
          flipY: false,
          rotate: "0",
          visibility: "visible",
        },
        type: "video",
        metadata: {
          previewUrl: localVideo.thumbnailUrl || localVideo.objectUrl,
          localVideoId: localVideo.id,
          fileName: localVideo.name,
          mouseTrackingData: mouseData, // Store mouse data in metadata
          recordingInfo: metadata
        },
        duration: localVideo.duration,
      };

      // Add to timeline automatically
      dispatch(ADD_VIDEO, {
        payload: videoData,
        options: {
          resourceId: "main",
          scaleMode: "fit",
        },
      });

      // Update state
      setState({
        isLoading: false,
        hasData: true,
        error: null,
        videoFile,
        mouseData,
        metadata
      });

      console.log('External recording data loaded successfully:', {
        videoFile: videoFile.name,
        mouseDataPoints: mouseData.length,
        metadata
      });

    } catch (error) {
      console.error('Error loading external data:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load external data'
      }));
      throw error;
    }
  }, [videoActions]);

  /**
   * Clear external data
   */
  const clearExternalData = useCallback(() => {
    setState({
      isLoading: false,
      hasData: false,
      error: null,
      videoFile: null,
      mouseData: [],
      metadata: null
    });
  }, []);

  /**
   * Check extension connection
   */
  const checkExtensionConnection = useCallback(async () => {
    try {
      return await extensionCommunication.pingExtension();
    } catch (error) {
      console.error('Error checking extension connection:', error);
      return false;
    }
  }, []);

  /**
   * Set up message listeners on mount
   */
  useEffect(() => {
    // Listen for recording complete messages
    extensionCommunication.onMessage('RECORDING_COMPLETE', handleRecordingComplete);
    extensionCommunication.onMessage('LOAD_RECORDING', handleRecordingComplete);

    // Cleanup on unmount
    return () => {
      extensionCommunication.offMessage('RECORDING_COMPLETE');
      extensionCommunication.offMessage('LOAD_RECORDING');
    };
  }, [handleRecordingComplete]);

  /**
   * Check for URL parameters on mount (for direct loading)
   */
  useEffect(() => {
    const checkUrlParams = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const loadExternal = urlParams.get('loadExternal');
      
      if (loadExternal === 'true') {
        console.log('External data loading requested via URL parameter');
        // Extension should send data shortly after page load
      }
    };

    checkUrlParams();
  }, []);

  return {
    state,
    loadExternalData,
    clearExternalData,
    checkExtensionConnection
  };
}
