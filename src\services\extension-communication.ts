/**
 * Extension Communication Service
 * Handles communication between the browser extension and the react-video-editor
 */

export interface MouseTrackingData {
  action: 'move' | 'click';
  coords: {
    x: number;
    y: number;
  };
  timestamp?: number;
}

export interface RecordingData {
  videoBlob: Blob;
  mouseData: MouseTrackingData[];
  recordingInfo: {
    totalCoordinates: number;
    totalClicks: number;
    timestamp: string;
    duration?: number;
    dimensions?: {
      width: number;
      height: number;
    };
  };
}

export interface ExtensionMessage {
  type: 'RECORDING_COMPLETE' | 'LOAD_RECORDING' | 'PING' | 'PONG';
  data?: RecordingData;
  source: 'extension' | 'editor';
  timestamp: number;
}

class ExtensionCommunicationService {
  private messageHandlers: Map<string, (message: ExtensionMessage) => void> = new Map();
  private isListening = false;

  constructor() {
    this.setupMessageListener();
  }

  /**
   * Set up message listener for communication with browser extension
   */
  private setupMessageListener() {
    if (this.isListening) return;

    window.addEventListener('message', (event) => {
      // Only accept messages from same origin for security
      if (event.origin !== window.location.origin) return;

      try {
        const message = event.data as ExtensionMessage;
        
        // Validate message structure
        if (!this.isValidExtensionMessage(message)) return;

        // Handle the message
        this.handleMessage(message);
      } catch (error) {
        console.error('Error handling extension message:', error);
      }
    });

    this.isListening = true;
    console.log('Extension communication service initialized');
  }

  /**
   * Validate incoming message structure
   */
  private isValidExtensionMessage(message: any): message is ExtensionMessage {
    return (
      message &&
      typeof message === 'object' &&
      typeof message.type === 'string' &&
      typeof message.source === 'string' &&
      typeof message.timestamp === 'number' &&
      ['RECORDING_COMPLETE', 'LOAD_RECORDING', 'PING', 'PONG'].includes(message.type) &&
      ['extension', 'editor'].includes(message.source)
    );
  }

  /**
   * Handle incoming messages
   */
  private handleMessage(message: ExtensionMessage) {
    console.log('Received extension message:', message.type, message);

    // Call registered handlers
    const handler = this.messageHandlers.get(message.type);
    if (handler) {
      handler(message);
    }

    // Send acknowledgment for recording complete messages
    if (message.type === 'RECORDING_COMPLETE' && message.source === 'extension') {
      this.sendMessage({
        type: 'PONG',
        source: 'editor',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Send message to extension
   */
  public sendMessage(message: Omit<ExtensionMessage, 'timestamp'>) {
    const fullMessage: ExtensionMessage = {
      ...message,
      timestamp: Date.now()
    };

    // Post message to parent window (extension context)
    window.parent.postMessage(fullMessage, window.location.origin);
    console.log('Sent message to extension:', fullMessage);
  }

  /**
   * Register a handler for specific message types
   */
  public onMessage(type: ExtensionMessage['type'], handler: (message: ExtensionMessage) => void) {
    this.messageHandlers.set(type, handler);
  }

  /**
   * Remove a message handler
   */
  public offMessage(type: ExtensionMessage['type']) {
    this.messageHandlers.delete(type);
  }

  /**
   * Check if extension is available by sending a ping
   */
  public async pingExtension(): Promise<boolean> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        this.offMessage('PONG');
        resolve(false);
      }, 2000);

      this.onMessage('PONG', () => {
        clearTimeout(timeout);
        this.offMessage('PONG');
        resolve(true);
      });

      this.sendMessage({
        type: 'PING',
        source: 'editor'
      });
    });
  }

  /**
   * Process recording data from extension
   */
  public processRecordingData(data: RecordingData): {
    videoFile: File;
    mouseData: MouseTrackingData[];
    metadata: RecordingData['recordingInfo'];
  } {
    // Convert blob to file with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const videoFile = new File([data.videoBlob], `screen-recording-${timestamp}.webm`, {
      type: 'video/webm'
    });

    return {
      videoFile,
      mouseData: data.mouseData,
      metadata: data.recordingInfo
    };
  }

  /**
   * Validate recording data structure
   */
  public isValidRecordingData(data: any): data is RecordingData {
    return (
      data &&
      typeof data === 'object' &&
      data.videoBlob instanceof Blob &&
      Array.isArray(data.mouseData) &&
      data.recordingInfo &&
      typeof data.recordingInfo === 'object' &&
      typeof data.recordingInfo.totalCoordinates === 'number' &&
      typeof data.recordingInfo.totalClicks === 'number' &&
      typeof data.recordingInfo.timestamp === 'string'
    );
  }
}

// Create singleton instance
export const extensionCommunication = new ExtensionCommunicationService();

// Export types for use in other components
export type { ExtensionMessage, RecordingData, MouseTrackingData };
