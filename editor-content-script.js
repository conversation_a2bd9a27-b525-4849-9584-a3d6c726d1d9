// Content script for video editor integration
// This script runs in the video editor page to receive data from the extension

(function() {
    'use strict';
    
    console.log('Video editor content script loaded');

    // Listen for messages from the extension
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('Editor content script received message:', message);
        
        if (message.type === 'RECORDING_COMPLETE' && message.source === 'extension') {
            // Forward the message to the page's window context
            window.postMessage(message, window.location.origin);
            sendResponse({ received: true, forwarded: true });
        }
        
        return true; // Keep message channel open
    });

    // Notify the extension that the editor is ready
    chrome.runtime.sendMessage({
        type: 'EDITOR_READY',
        source: 'editor',
        timestamp: Date.now(),
        url: window.location.href
    }).catch(() => {
        // Extension might not be available, which is fine
        console.log('Extension not available for editor ready notification');
    });

    console.log('Video editor content script initialized');
})();
