import React, { useEffect, useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { CheckCircle, X, Monitor, Mouse, ExternalLink } from 'lucide-react';
import { useExternalDataLoader } from '../hooks/use-external-data-loader';
import { cn } from '@/lib/utils';

interface ExternalDataNotificationProps {
  className?: string;
}

export function ExternalDataNotification({ className }: ExternalDataNotificationProps) {
  const { state } = useExternalDataLoader();
  const [isVisible, setIsVisible] = useState(false);
  const [hasShownSuccess, setHasShownSuccess] = useState(false);

  // Show notification when data is loaded successfully
  useEffect(() => {
    if (state.hasData && !state.isLoading && !state.error && !hasShownSuccess) {
      setIsVisible(true);
      setHasShownSuccess(true);
      
      // Auto-hide after 10 seconds
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 10000);
      
      return () => clearTimeout(timer);
    }
  }, [state.hasData, state.isLoading, state.error, hasShownSuccess]);

  // Reset when new data loading starts
  useEffect(() => {
    if (state.isLoading) {
      setHasShownSuccess(false);
    }
  }, [state.isLoading]);

  if (!isVisible || !state.hasData || state.error) {
    return null;
  }

  const formatMouseStats = () => {
    if (!state.metadata) return { coordinates: 0, clicks: 0 };
    return {
      coordinates: state.metadata.totalCoordinates,
      clicks: state.metadata.totalClicks
    };
  };

  const stats = formatMouseStats();

  return (
    <div className={cn("fixed top-4 right-4 z-50 max-w-md", className)}>
      <Alert className="border-green-200 bg-green-50 shadow-lg">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="font-medium">Recording loaded from extension!</div>
              <div className="text-sm space-y-1">
                <div className="flex items-center gap-2">
                  <Monitor className="h-3 w-3" />
                  <span>Video: {state.videoFile?.name || 'Unknown'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mouse className="h-3 w-3" />
                  <span>Mouse: {stats.coordinates} points, {stats.clicks} clicks</span>
                </div>
              </div>
              <div className="text-xs opacity-75">
                Video and mouse tracking data have been automatically added to your timeline.
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0 text-green-600 hover:text-green-800 hover:bg-green-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
}

export default ExternalDataNotification;
