"use client";
import Timeline from "./timeline";
import useStore from "./store/use-store";
import useTimelineEvents from "./hooks/use-timeline-events";
import Scene from "./scene";
import StateManager from "@designcombo/state";
import { useEffect, useRef, useState } from "react";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { ImperativePanelHandle } from "react-resizable-panels";

import { LocalMedia } from "./menu-item/local-media";
import { useDynamicSceneSizing } from "./hooks/use-dynamic-scene-sizing";
import { StickyExportButton } from "./components/sticky-export-button";
import CPUDebugPanel from "./debug/cpu-debug-panel";
import { useExternalDataLoader } from "./hooks/use-external-data-loader";
import { ExternalDataNotification } from "./components/external-data-notification";




const stateManager = new StateManager({
  size: {
    width: 1080,
    height: 1920,
  },
});

const Editor = () => {
  const timelinePanelRef = useRef<ImperativePanelHandle>(null);
  const { timeline, playerRef } = useStore();
  const [showCPUDebug, setShowCPUDebug] = useState(false);

  useTimelineEvents();
  useDynamicSceneSizing(stateManager);

  // Initialize external data loader for extension integration
  useExternalDataLoader();



  useEffect(() => {
    const screenHeight = window.innerHeight;
    const desiredHeight = 300;
    const percentage = (desiredHeight / screenHeight) * 100;
    timelinePanelRef.current?.resize(percentage);
  }, []);

  const handleTimelineResize = () => {
    const timelineContainer = document.getElementById("timeline-container");
    if (!timelineContainer) return;

    timeline?.resize(
      {
        height: timelineContainer.clientHeight - 90,
        width: timelineContainer.clientWidth - 40,
      },
      {
        force: true,
      },
    );
  };

  useEffect(() => {
    const onResize = () => handleTimelineResize();
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, [timeline]);

  // Toggle CPU debug panel with Ctrl+Shift+C
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        setShowCPUDebug(!showCPUDebug);
        console.log('🔥 CPU Debug Panel toggled:', !showCPUDebug);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showCPUDebug]);

  return (
    <div className="flex h-screen w-screen">
      {/* Sidebar that goes all the way down */}
      <div className="bg-sidebar flex flex-none border-r border-border/80 w-[300px]">
        <LocalMedia />
      </div>

      {/* Main content area with scene and timeline */}
      <div className="flex flex-1 flex-col">
        <ResizablePanelGroup style={{ flex: 1 }} direction="vertical">
          <ResizablePanel className="relative" defaultSize={70}>
            <div
              style={{
                width: "100%",
                height: "100%",
                position: "relative",
                flex: 1,
                overflow: "hidden",
              }}
            >
              <Scene stateManager={stateManager} />
            </div>
          </ResizablePanel>
          <ResizableHandle />
          <ResizablePanel
            className="min-h-[50px]"
            ref={timelinePanelRef}
            defaultSize={30}
            onResize={handleTimelineResize}
          >
            {playerRef && <Timeline stateManager={stateManager} />}
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>

      {/* Sticky Export Button */}
      <StickyExportButton />

      {/* CPU Debug Panel */}
      <CPUDebugPanel
        isVisible={showCPUDebug}
        onToggle={() => setShowCPUDebug(!showCPUDebug)}
      />

      {/* External Data Notification */}
      <ExternalDataNotification />
    </div>
  );
};

export default Editor;
