import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  Monitor, 
  Mouse, 
  ExternalLink,
  RefreshCw,
  X
} from 'lucide-react';
import { useExternalDataLoader } from '../hooks/use-external-data-loader';
import { cn } from '@/lib/utils';

interface ExternalDataStatusProps {
  className?: string;
  onDataLoaded?: () => void;
}

export function ExternalDataStatus({ className, onDataLoaded }: ExternalDataStatusProps) {
  const { state, clearExternalData, checkExtensionConnection } = useExternalDataLoader();
  const [extensionConnected, setExtensionConnected] = useState<boolean | null>(null);
  const [isCheckingConnection, setIsCheckingConnection] = useState(false);

  /**
   * Check extension connection on mount and periodically
   */
  useEffect(() => {
    const checkConnection = async () => {
      setIsCheckingConnection(true);
      const connected = await checkExtensionConnection();
      setExtensionConnected(connected);
      setIsCheckingConnection(false);
    };

    checkConnection();

    // Check connection every 30 seconds
    const interval = setInterval(checkConnection, 30000);
    return () => clearInterval(interval);
  }, [checkExtensionConnection]);

  /**
   * Call onDataLoaded when data is successfully loaded
   */
  useEffect(() => {
    if (state.hasData && !state.isLoading && !state.error) {
      onDataLoaded?.();
    }
  }, [state.hasData, state.isLoading, state.error, onDataLoaded]);

  /**
   * Manual connection check
   */
  const handleCheckConnection = async () => {
    setIsCheckingConnection(true);
    const connected = await checkExtensionConnection();
    setExtensionConnected(connected);
    setIsCheckingConnection(false);
  };

  /**
   * Format mouse data statistics
   */
  const formatMouseStats = () => {
    if (!state.metadata) return null;
    
    return {
      coordinates: state.metadata.totalCoordinates,
      clicks: state.metadata.totalClicks,
      duration: state.metadata.duration ? `${(state.metadata.duration / 1000).toFixed(1)}s` : 'Unknown'
    };
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            <CardTitle className="text-lg">Extension Integration</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {/* Extension Connection Status */}
            <Badge 
              variant={extensionConnected ? "default" : "secondary"}
              className={cn(
                "flex items-center gap-1",
                extensionConnected ? "bg-green-500 hover:bg-green-600" : "bg-gray-500"
              )}
            >
              {isCheckingConnection ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : extensionConnected ? (
                <CheckCircle className="h-3 w-3" />
              ) : (
                <AlertCircle className="h-3 w-3" />
              )}
              {isCheckingConnection ? 'Checking...' : extensionConnected ? 'Connected' : 'Disconnected'}
            </Badge>
            
            {/* Refresh Connection Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCheckConnection}
              disabled={isCheckingConnection}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("h-4 w-4", isCheckingConnection && "animate-spin")} />
            </Button>
          </div>
        </div>
        <CardDescription>
          Status of browser extension integration for screen recording with mouse tracking
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Loading State */}
        {state.isLoading && (
          <Alert>
            <Loader2 className="h-4 w-4 animate-spin" />
            <AlertDescription>
              Loading recording data from browser extension...
            </AlertDescription>
          </Alert>
        )}

        {/* Error State */}
        {state.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>{state.error}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearExternalData}
                className="h-6 w-6 p-0 ml-2"
              >
                <X className="h-4 w-4" />
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Success State - Data Loaded */}
        {state.hasData && !state.isLoading && !state.error && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <div className="flex items-center justify-between">
                <span>Recording data loaded successfully!</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearExternalData}
                  className="h-6 w-6 p-0 ml-2 text-green-600 hover:text-green-800"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Data Details */}
        {state.hasData && state.metadata && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Recording Details</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Monitor className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Video:</span>
                </div>
                <div className="text-muted-foreground ml-6">
                  {state.videoFile?.name || 'Unknown'}
                </div>
              </div>
              
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Mouse className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Mouse Data:</span>
                </div>
                <div className="text-muted-foreground ml-6">
                  {formatMouseStats()?.coordinates || 0} points, {formatMouseStats()?.clicks || 0} clicks
                </div>
              </div>
            </div>
            
            <div className="text-xs text-muted-foreground">
              Recorded: {new Date(state.metadata.timestamp).toLocaleString()}
            </div>
          </div>
        )}

        {/* Extension Not Connected */}
        {extensionConnected === false && !state.hasData && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p>Browser extension not detected. To use screen recording with mouse tracking:</p>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  <li>Install the Mouse Tracker & Screen Recorder extension</li>
                  <li>Open the extension and start a recording</li>
                  <li>The video and mouse data will automatically load here</li>
                </ol>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Extension Connected but No Data */}
        {extensionConnected === true && !state.hasData && !state.isLoading && (
          <Alert>
            <CheckCircle className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              Extension connected! Start a recording in the browser extension to automatically load video and mouse tracking data here.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}

export default ExternalDataStatus;
